:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.container {
    max-width: 1200px;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.btn {
    border-radius: 8px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.alert {
    border-radius: 8px;
}

.form-control {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid #dee2e6;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1.5rem;
}

p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.highlight {
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
}

.highlight.red {
    background-color: #ffe6e6;
    color: #dc3545;
}

.highlight.green {
    background-color: #e6ffee;
    color: #198754;
}

.highlight.yellow {
    background-color: #fff9e6;
    color: #ffc107;
}

@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }
}

/* Highlight Classes */
.strikethrough {
    text-decoration: line-through;
    color: #999;
}

.red {
    background-color: #ffcccc;
    padding: 2px 5px;
    border-radius: 3px;
    font-style: italic;
    /* Added italic style */
}

.green {
    background-color: #ccffcc;
    padding: 2px 5px;
    border-radius: 3px;
}

.yellow {
    background-color: #ffffcc;
    padding: 2px 5px;
    border-radius: 3px;
}

/* Spinner and Checkmark Animation */
.spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
    vertical-align: middle;
}

.question-info p {
    margin-bottom: 0.5rem;
}

.checkmark {
    display: none;
    width: 40px;
    height: 40px;
    margin-left: 10px;
    vertical-align: middle;
}

.checkmark path {
    stroke: #198754;
    stroke-width: 4;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
    animation: draw 0.5s ease forwards;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes draw {
    to {
        stroke-dashoffset: 0;
    }
}

button[disabled] {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Tooltip Styling */
/* Remove or comment out the conflicting tooltip styles */
/* body .custom-tooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

body .custom-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    font-size: 0.9em;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
}

.custom-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
} */
.custom-tooltip {
    position: relative;
    cursor: help;
    display: inline-block;
}

.custom-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -100%);
    padding: 4px 8px;
    background-color: #1f2937;
    color: white;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    z-index: 50;
    margin-top: -4px;
    pointer-events: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

.yellow {
    background-color: #fef3c7;
}

.green {
    background-color: #d1fae5;
}

.red {
    background-color: #fee2e2;
}

.strikethrough {
    text-decoration: line-through;
    color: #6b7280;
}

/* Add these styles to ensure proper height calculation */
html, body {
    height: 100%;
    margin: 0;
}

.h-full {
    height: 100%;
}

/* Add smooth scrolling to the page */
html {
    scroll-behavior: smooth;
}

/* Add these styles to ensure proper scrolling */
.overflow-y-auto {
    scrollbar-width: thin;
    scrollbar-color: #CBD5E1 #F1F5F9;
}

.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #F1F5F9;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: #CBD5E1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background-color: #94A3B8;
}

/* Calculator button styles */
.calc-btn {
    @apply bg-white text-gray-700 border border-gray-300 rounded-md py-2 px-4 text-sm font-medium
    hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2
    transition-colors duration-200;
}

/* MathQuill custom styles */
.mq-editable-field {
    @apply border-0 shadow-none w-full min-h-[60px] text-base;
}

.mq-editable-field.mq-focused {
    @apply border-0 shadow-none;
}

/* Enhanced grid-row styling */
.grid-row {
    transition: background-color 0.2s ease-in-out;
}

.grid-row:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Evidence text styling */
.evidence-text {
    font-style: italic;
    border-left: 3px solid currentColor;
    padding-left: 0.75rem;
}

/* Loading animation enhancements */
.animate-spin {
    animation-duration: 0.8s;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Form elements styling */
textarea {
    transition: all 0.2s ease;
}

textarea:focus {
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    outline: none;
    transform: translateY(-1px);
}

button {
    transition: all 0.2s ease;
}

button:hover:not(:disabled) {
    transform: translateY(-1px);
}

button:active:not(:disabled) {
    transform: translateY(0px);
}

/* Grid layout improvements */
.grid-row {
    transition: all 0.2s ease-in-out;
}

.grid-row:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Card hover effects */
.rounded-lg {
    transition: box-shadow 0.2s ease;
}

.rounded-lg:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* LaTeX preview styling */
.latex-preview {
    min-height: 2rem;
    border-left: 3px solid #e5e7eb;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.latex-preview:empty::before {
    content: "LaTeX preview will appear here";
    color: #9ca3af;
    font-style: italic;
}

.latex-content {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Ensure proper spacing for inline math */
.inline-math {
    margin: 0 0.1em;
}

/* SAQ Feedback Click-to-Toggle Styles */
.saq-feedback-container {
    min-height: 2rem;
    cursor: pointer;
}

.saq-feedback-hidden {
    transition: filter 0.3s ease-in-out;
    filter: blur(4px); /* Default blurred state */
}

.saq-hover-hint {
    backdrop-filter: blur(2px);
    border: 1px dashed #d1d5db;
    transition: opacity 0.3s ease-in-out;
}

/* Toggle states for click functionality */
.saq-feedback-container.revealed .saq-feedback-hidden {
    filter: blur(0px) !important;
}

.saq-feedback-container.revealed .saq-hover-hint {
    opacity: 0 !important;
}

/* SAQ Correct Answer Click-to-Toggle Styles */
.saq-correct-answer-container {
    min-height: 2rem;
    cursor: pointer;
}

.saq-correct-answer-hidden {
    transition: filter 0.3s ease-in-out;
    filter: blur(4px); /* Default blurred state */
}

.saq-hover-hint {
    backdrop-filter: blur(2px);
    border: 1px dashed #d1d5db;
    transition: opacity 0.3s ease-in-out;
}

/* Toggle states for click functionality */
.saq-correct-answer-container.revealed .saq-correct-answer-hidden {
    filter: blur(0px) !important;
}

.saq-correct-answer-container.revealed .saq-hover-hint {
    opacity: 0 !important;
}

/* Add subtle animation to the eye icon */
.saq-hover-hint svg {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* ===== MULTI-COLOR HIGHLIGHTING SYSTEM ===== */

/* Base highlighting styles - minimalistic approach */
.highlight-base {
    padding: 1px 3px;
    border-radius: 2px;
    position: relative;
    transition: background-color 0.15s ease;
}

/* Partial achievement indicator - dotted underline in marking point color */
.highlight-partial.highlight-yellow {
    border-bottom: 2px dotted #f59e0b;
}

.highlight-partial.highlight-blue {
    border-bottom: 2px dotted #3b82f6;
}

.highlight-partial.highlight-green {
    border-bottom: 2px dotted #10b981;
}

.highlight-partial.highlight-pink {
    border-bottom: 2px dotted #ec4899;
}

.highlight-partial.highlight-purple {
    border-bottom: 2px dotted #8b5cf6;
}

.highlight-partial.highlight-indigo {
    border-bottom: 2px dotted #6366f1;
}

.highlight-partial.highlight-teal {
    border-bottom: 2px dotted #14b8a6;
}

.highlight-partial.highlight-orange {
    border-bottom: 2px dotted #f97316;
}

.highlight-partial.highlight-lime {
    border-bottom: 2px dotted #84cc16;
}

.highlight-partial.highlight-cyan {
    border-bottom: 2px dotted #06b6d4;
}

/* For multi-color partial highlights, use a neutral dotted underline */
.highlight-partial.highlight-multi-3,
.highlight-partial.highlight-multi-4plus {
    border-bottom: 2px dotted #6b7280;
}

/* Single color highlights - clean backgrounds only, no borders */
.highlight-yellow {
    background-color: #fef3c7;
}

.highlight-blue {
    background-color: #dbeafe;
}

.highlight-green {
    background-color: #d1fae5;
}

.highlight-pink {
    background-color: #fce7f3;
}

.highlight-purple {
    background-color: #e9d5ff;
}

.highlight-indigo {
    background-color: #e0e7ff;
}

.highlight-teal {
    background-color: #ccfbf1;
}

.highlight-orange {
    background-color: #fed7aa;
}

.highlight-lime {
    background-color: #ecfccb;
}

.highlight-cyan {
    background-color: #cffafe;
}

/* Two-color combinations using subtle gradients - no borders */
.highlight-yellow.highlight-blue {
    background: linear-gradient(90deg, #fef3c7 50%, #dbeafe 50%);
}

.highlight-yellow.highlight-green {
    background: linear-gradient(90deg, #fef3c7 50%, #d1fae5 50%);
}

.highlight-yellow.highlight-pink {
    background: linear-gradient(90deg, #fef3c7 50%, #fce7f3 50%);
}

.highlight-yellow.highlight-purple {
    background: linear-gradient(90deg, #fef3c7 50%, #e9d5ff 50%);
}

.highlight-yellow.highlight-indigo {
    background: linear-gradient(90deg, #fef3c7 50%, #e0e7ff 50%);
}

.highlight-blue.highlight-green {
    background: linear-gradient(90deg, #dbeafe 50%, #d1fae5 50%);
}

.highlight-blue.highlight-pink {
    background: linear-gradient(90deg, #dbeafe 50%, #fce7f3 50%);
}

.highlight-blue.highlight-purple {
    background: linear-gradient(90deg, #dbeafe 50%, #e9d5ff 50%);
}

.highlight-green.highlight-pink {
    background: linear-gradient(90deg, #d1fae5 50%, #fce7f3 50%);
}

.highlight-green.highlight-purple {
    background: linear-gradient(90deg, #d1fae5 50%, #e9d5ff 50%);
}

/* Three-color combinations using distinct sections */
.highlight-multi-3 {
    background: linear-gradient(
        90deg,
        #fef3c7 0%,
        #fef3c7 33.33%,
        #dbeafe 33.33%,
        #dbeafe 66.66%,
        #d1fae5 66.66%,
        #d1fae5 100%
    );
    position: relative;
}

/* Four or more colors - distinct sections */
.highlight-multi-4plus {
    background: linear-gradient(
        90deg,
        #fef3c7 0%,
        #fef3c7 25%,
        #dbeafe 25%,
        #dbeafe 50%,
        #d1fae5 50%,
        #d1fae5 75%,
        #fce7f3 75%,
        #fce7f3 100%
    );
    position: relative;
}

/* Small indicator for 4+ colors - minimal and unobtrusive */
.highlight-multi-4plus::before {
    content: "●";
    position: absolute;
    top: -1px;
    right: 0px;
    font-size: 6px;
    color: #666;
    opacity: 0.6;
}

/* Subtle hover effects for all highlights */
.highlight-base:hover {
    opacity: 0.9;
}

/* Mobile responsiveness for highlights */
@media (max-width: 768px) {
    .highlight-base {
        padding: 1px 2px;
        font-size: 14px;
    }

    .highlight-multi-4plus::before {
        font-size: 5px;
    }
}

/* Accessibility improvements */
.highlight-base:focus {
    outline: 1px solid #3b82f6;
    outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .highlight-base {
        font-weight: 600;
    }

    .highlight-multi-3,
    .highlight-multi-4plus {
        opacity: 1;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .highlight-base {
        transition: none;
    }
}

