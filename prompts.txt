please clean up the /question/id/submissions page, instead of displaying the raw json data you should display something like /submission_details page. 

when the user is in mobile vertical mode, can you make it that the feedback on /question is stacked vertically instead of side by side? basically have the highlighting results on top of the marking points grading results. and please optimise the display for mobile so that the feedback is more readable and user-friendly, without changing the overall structure of the page.

can you add another portion of the feedback: for marking points which only got partial, can you ask the Groq (kimik2) to generate what exactly the user MISSED out on the answer, and BOLD the part of the answer that the user missed on the marking point feedback? (without changing the text of the marking point, just bold the part that the user missed).