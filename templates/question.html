{% extends "base.html" %}
{% block head %}
{{ super() }}
<!-- Add required libraries for Markdown and LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/mathquill/0.10.1/mathquill.min.js"></script>
<!-- Include KaTeX for LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/contrib/auto-render.min.js"></script>

<!-- Include questionjs.js for question functionality -->
<script src="{{ url_for('static', filename='js/questionjs.js') }}"></script>

<!-- Initialize MathQuill -->
<script>
    // Initialize MathQuill interface
    var MQ = MathQuill.getInterface(2);
    var mathFields = {}; // Initialize mathFields as a global variable
</script>

<!-- Custom styles for LaTeX rendering and prose typography -->
<style>
    /* Ensure inline LaTeX is properly aligned with text */
    .katex-inline {
        display: inline-block;
        vertical-align: middle;
    }

    /* Add some spacing around inline LaTeX */
    .katex {
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing */
    .katex-display {
        margin: 1em 0;
    }

    /* Make sure inline LaTeX doesn't break the line flow */
    p {
        display: block;
        line-height: 1.5;
        margin: 1em 0;
    }

    /* Custom prose styles for explanation content */
    .prose {
        color: #374151;
        max-width: none;
    }

    .prose h3 {
        color: #111827;
        font-weight: 600;
        font-size: 1.125rem;
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    .prose h4 {
        color: #1f2937;
        font-weight: 600;
        font-size: 1rem;
        margin-top: 1.25rem;
        margin-bottom: 0.5rem;
        line-height: 1.5;
    }

    .prose h5 {
        color: #1f2937;
        font-weight: 600;
        font-size: 0.875rem;
        margin-top: 1rem;
        margin-bottom: 0.25rem;
        line-height: 1.4;
    }

    .prose p {
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    .prose li {
        margin-left: 1rem;
        margin-bottom: 0.25rem;
        line-height: 1.6;
    }

    .prose ul {
        list-style-type: disc;
        margin-bottom: 1rem;
        padding-left: 1rem;
    }

    .prose ol {
        list-style-type: decimal;
        margin-bottom: 1rem;
        padding-left: 1rem;
    }

    .prose strong {
        font-weight: 600;
        color: #111827;
    }

    .prose em {
        font-style: italic;
    }

    .prose code {
        background-color: #f3f4f6;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    }

    .prose blockquote {
        border-left: 4px solid #d1d5db;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: #6b7280;
    }

    /* Ensure LaTeX content renders properly within prose */
    .prose .latex-content .katex {
        margin: 0 0.1em;
    }

    .prose .latex-content .katex-display {
        margin: 1em 0;
    }

    /* Smooth animations for explanation reveal */
    .explanation-container {
        transition: all 0.3s ease-in-out;
        transform-origin: top;
    }

    .explanation-container.hidden {
        opacity: 0;
        transform: scaleY(0);
        max-height: 0;
        overflow: hidden;
    }

    .explanation-container:not(.hidden) {
        opacity: 1;
        transform: scaleY(1);
        max-height: none;
    }

    /* Enhanced button hover effects */
    .explain-button {
        transition: all 0.2s ease-in-out;
        transform: translateY(0);
    }

    .explain-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
    }
</style>

    /* Ensure inline elements are properly aligned */
    .katex-html {
        display: inline-block;
        vertical-align: middle;
    }

    /* Style for our custom inline math wrapper */
    .inline-math {
        display: inline;
        vertical-align: baseline;
        margin: 0 0.1em;
    }

    /* Style for code blocks in markdown */
    pre {
        background-color: #f5f5f5;
        padding: 0.5em;
        border-radius: 0.25em;
        overflow-x: auto;
    }

    /* Style for inline code in markdown */
    code {
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 0.25em;
        font-family: monospace;
    }

    /* Style for paragraphs in the preview */
    p {
        margin-bottom: 1em;
    }

    /* Style for headings in the preview */
    h1, h2, h3, h4, h5, h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: bold;
    }

    /* Style for lists in the preview */
    ul, ol {
        margin-left: 1.5em;
        margin-bottom: 1em;
    }

    /* SAQ Correct Answer Click-to-Toggle Styles */
    .saq-correct-answer-container {
        min-height: 2rem;
        cursor: pointer;
    }

    .saq-correct-answer-hidden {
        transition: filter 0.3s ease-in-out;
        filter: blur(4px); /* Default blurred state */
    }

    .saq-hover-hint {
        backdrop-filter: blur(2px);
        border: 1px dashed #d1d5db;
        transition: opacity 0.3s ease-in-out;
    }

    /* SAQ Feedback Click-to-Toggle Styles */
    .saq-feedback-container {
        min-height: 2rem;
        cursor: pointer;
    }

    .saq-feedback-hidden {
        transition: filter 0.3s ease-in-out;
        filter: blur(4px); /* Default blurred state */
    }

    /* Toggle states for click functionality */
    .saq-correct-answer-container.revealed .saq-correct-answer-hidden {
        filter: blur(0px) !important;
    }

    .saq-correct-answer-container.revealed .saq-hover-hint {
        opacity: 0 !important;
    }

    .saq-feedback-container.revealed .saq-feedback-hidden {
        filter: blur(0px) !important;
    }

    .saq-feedback-container.revealed .saq-hover-hint {
        opacity: 0 !important;
    }

    /* Add subtle animation to the eye icon */
    .saq-hover-hint svg {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Add MathQuill dependencies in the head -->


<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-12 gap-6">
        <!-- Left Column: Current Question -->
        <div class="col-span-9">
            {% if feedback != None %}
                <div id="feedback-container" class="mb-6 bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                    <div class="p-6 prose prose-sm max-w-none">{{ feedback | safe }}</div>
                </div>
            {% endif %}

            <!-- Question Card -->
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6 min-h-[100px]">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-3">
                        <h2 class="text-lg font-semibold text-gray-900">
                        {% if question.source %}
                            [{{ question.source}}] {{ question.title }}</h2>
                        {% else %}
                            {{ question.title }}</h2>
                        {% endif %}
                        {% if session.user_id %}
                            {% set user = User.query.get(session['user_id']) %}
                            <div class="flex flex-col space-y-2">
                                <!-- Clarification button for all logged-in users -->
                                <button onclick="openClarificationModal({{ question.id }})"
                                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-question-circle mr-1.5"></i> Ask Teacher
                                </button>

                                {% if user and user.is_admin %}
                                <a href="{{ url_for('edit_question', question_id=question.id) }}"
                                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-edit mr-1.5"></i> Edit Question
                                </a>
                                <a href="{{ url_for('question_submissions', question_id=question.id) }}"
                                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <i class="fas fa-list-alt mr-1.5"></i> View Submissions
                                </a>
                                <a href="#"
                                    onclick="confirmDelete({{ question.id }})"
                                    class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-trash mr-1.5"></i> Delete Question
                                </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                    <p class="text-sm text-gray-500 mb-4">
                        <span class="font-medium">Topic:</span>
                        {% if question.topic %}
                            {{ question.topic.name }}
                        {% else %}
                            <span class="text-gray-400">No topic</span>
                        {% endif %}
                    </p>

                    <!-- {% if question.attachment != None %}
                    <div class="mb-4">
                        <img src="{{ url_for('serve.serve_file', filename=question.attachment) }}"
                             class="rounded-lg max-h-[600px] w-auto mx-auto"
                             alt="Question attachment">
                    </div>
                    {% endif %} -->

                    <p class="text-gray-700">
                        <!-- {% if question.description != None %}
                            {{ question.description | safe }}
                        {% endif %} -->
                        <span class="text-sm text-gray-500">
                            [{% set total_score = question.parts | sum(attribute='score') %}{{ "%.1f"|format(total_score) if total_score != 0 else 'N/A' }}m]
                        </span>
                    </p>
                </div>
            </div>
            <!-- Parts -->
            {% for part in question.parts %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
                <div class="p-6">
                    <div class="text-md text-gray-900 mb-4">
                        <div class="part-description" data-content="{{ part.description }}"></div>
                        <span class="text-sm text-gray-500">[{{ part.score }}m]</span>
                    </div>

                    {% if part.attachments != None %}
                    <div class="mb-4">
                        {% for attachment in part.attachments %}
                        <img src="{{ url_for('serve.serve_file', filename=attachment.filename) }}"
                             class="rounded-lg max-h-[400px] w-auto mx-auto"
                             alt="Part attachment">
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Past Submissions Section -->
                    {% set user_submissions = part.submissions|selectattr('user_id', 'equalto', session['user_id'])|list if session.user_id else [] %}
                    {% if user_submissions and user_submissions|length > 0 %}
                    <div class="mb-6 bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900">Your Past Submissions</h4>
                            <button data-part-id="{{ part.id }}" class="toggle-submissions text-xs text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                                Show All
                            </button>
                        </div>
                        <div id="submissions-{{ part.id }}" class="space-y-2 hidden">
                            {% for submission in user_submissions|sort(attribute='timestamp', reverse=true) %}
                                <a href="{{ url_for('submission_details', submission_id=submission.id) }}"
                                   class="block bg-white p-3 rounded-lg border border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50 transition-all duration-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="flex h-2 w-2 rounded-full {% if submission.score == part.score %}bg-green-500{% elif submission.score > 0 %}bg-yellow-500{% else %}bg-red-500{% endif %}"></span>
                                            <span class="text-sm font-medium text-gray-900">{% if submission.score == part.score %}Correct{% elif submission.score > 0 %}Partial{% else %}Incorrect{% endif %}</span>
                                            <span class="text-xs text-gray-500">by {{ submission.user.username }}</span>
                                        </div>
                                        <span class="text-xs text-gray-500">{{ submission.timestamp.strftime('%b %d, %H:%M') }}</span>
                                    </div>
                                    <div class="mt-2 flex items-center justify-between">
                                        <div class="text-sm text-gray-600">Score: {{ submission.score }}</div>
                                        <span class="text-xs text-indigo-600">View Details →</span>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {%if part.input_type == 'mcq'%}
                    <form
                        method="post"
                        action="{{ url_for('get_git_diff', question_id=question.id, part_id=part.id) }}"
                        onsubmit="return submitAnswer(event, {{ part.id }}, {{ question.id }});"
                        class="space-y-4"
                        data-part-id="{{ part.id }}"
                        data-input-type="{{ 'mcq' }}"
                    >
                    {%else%}
                     <form
                        method="post"
                        action="{{ url_for('get_git_diff', question_id=question.id, part_id=part.id) }}"
                        onsubmit="return submitAnswer(event, {{ part.id }}, {{ question.id }});"
                        class="space-y-4"
                        data-part-id="{{ part.id }}"
                        data-input-type="{{ 'saq' }}"
                    >
                    {%endif%}
                        <!-- SAQ Input (default) -->
                        {% if not part.input_type == 'mcq' %}
                        <div class="saq-input">
                            <label for="answer_{{ part.id }}" class="block text-sm font-medium text-gray-700 mb-1"></label>
                            <div class="space-y-4">
                                <textarea
                                    class="block w-full rounded-md border-0 py-3 px-4 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    id="answer_{{ part.id }}"
                                    name="answer"
                                    rows="4"
                                    placeholder="Enter your answer here..."></textarea>

                                <!-- Image Upload Section -->
                                <div class="flex items-center space-x-4">
                                    <label for="image_{{ part.id }}" class="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer">
                                        <i class="fas fa-camera mr-2"></i>
                                        Upload Image
                                        <input type="file"
                                               id="image_{{ part.id }}"
                                               name="image"
                                               accept="image/png,image/jpeg,image/jpg"
                                               class="hidden"
                                               onchange="previewImage(this, 'preview_{{ part.id }}')">
                                    </label>
                                    <div id="preview_{{ part.id }}" class="hidden">
                                        <img src="" alt="Preview" class="max-h-32 rounded-lg shadow-sm">
                                        <button type="button"
                                                onclick="removeImage('image_{{ part.id }}', 'preview_{{ part.id }}')"
                                                class="ml-2 text-red-600 hover:text-red-800">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else%}
                        <!-- MCQ Input -->
                        <div class="mcq-input">
                            <fieldset>
                                <legend class="block text-sm font-medium text-gray-700 mb-3">Select the correct answer:</legend>
                                <div class="space-y-3" id="mcq_options_{{ part.id }}">
                                    {% for option in part.options %}
                                    <div class="mcq-option relative flex items-start">
                                        <div class="flex items-center h-5">
                                            <input
                                                id="mcq_option_{{ part.id }}_{{ loop.index0 }}"
                                                name="answer"
                                                type="radio"
                                                value="{{ loop.index0 }}"
                                                class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                                            >
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="mcq_option_{{ part.id }}_{{ loop.index0 }}" class="font-medium text-gray-700">
                                                <div class="option-content">{{ option.description | safe }}</div>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </fieldset>
                        </div>
                        {% endif %}

                        <!-- Calculator toggle button -->
                        <div class="flex justify-end mb-2">
                            <button type="button"
                                    onclick="toggleCalculator({{ part.id }})"
                                    class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-indigo-600 hover:text-indigo-500">
                                <svg class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zM5.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm9 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm-9 3a1.5 1.5 0 100 3 1.5 1.5 0 000-3zm9 0a1.5 1.5 0 100 3 1.5 1.5 0 000-3z" clip-rule="evenodd"/>
                                </svg>
                                <span id="calc_toggle_text_{{ part.id }}">Show Calculator</span>
                            </button>
                        </div>

                        <!-- Calculator interface (hidden by default) -->
                        <div id="calculator_{{ part.id }}" class="hidden">
                            <!-- MathQuill input field -->
                            <div class="mb-4">
                                <div id="math_input_{{ part.id }}" class="border rounded-md p-2 min-h-[60px] focus-within:ring-2 focus-within:ring-indigo-600 focus-within:border-indigo-600"></div>
                            </div>
                            <!-- Calculator buttons -->
                            <div class="grid grid-cols-4 gap-2 max-w-md mx-auto mb-4">
                                <!-- Basic operators -->
                                <button type="button" onclick="insertMath({{ part.id }}, '+')" class="calc-btn">+</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '-')" class="calc-btn">−</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\times')" class="calc-btn">×</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\div')" class="calc-btn">÷</button>

                                <!-- Numbers -->
                                <button type="button" onclick="insertMath({{ part.id }}, '7')" class="calc-btn">7</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '8')" class="calc-btn">8</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '9')" class="calc-btn">9</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '(')" class="calc-btn">(</button>

                                <button type="button" onclick="insertMath({{ part.id }}, '4')" class="calc-btn">4</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '5')" class="calc-btn">5</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '6')" class="calc-btn">6</button>
                                <button type="button" onclick="insertMath({{ part.id }}, ')')" class="calc-btn">)</button>

                                <button type="button" onclick="insertMath({{ part.id }}, '1')" class="calc-btn">1</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '2')" class="calc-btn">2</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '3')" class="calc-btn">3</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '^')" class="calc-btn">^</button>

                                <button type="button" onclick="insertMath({{ part.id }}, '0')" class="calc-btn">0</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '.')" class="calc-btn">.</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\sqrt')" class="calc-btn">√</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\frac')" class="calc-btn">⅟</button>

                                <!-- Advanced functions -->
                                <button type="button" onclick="insertMath({{ part.id }}, '\\sin')" class="calc-btn">sin</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\cos')" class="calc-btn">cos</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\tan')" class="calc-btn">tan</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\pi')" class="calc-btn">π</button>
                                <button type="button" onclick="insertMath({{ part.id }}, '\\ln')" class="calc-btn">ln</button>

                            </div>
                            <!-- Insert button -->
                            <div class="flex justify-end mb-4">
                                <button type="button"
                                        onclick="insertToTextarea({{ part.id }})"
                                        class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-500 rounded-md">
                                    Insert Expression into Answer
                                </button>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button
                                type="submit"
                                class="inline-flex justify-center py-2.5 px-5 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                id="submit-part-{{ part.id }}"
                            >
                                Submit Part {{ loop.index }}
                            </button>
                        </div>

                        <!-- Loading indicator -->
                        <div id="loading_{{ part.id }}" class="hidden">
                            <div class="flex flex-col items-center justify-center py-4">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-t-2 border-indigo-600 mb-2"></div>
                                <p class="text-sm text-gray-600">Processing your answer...</p>
                            </div>
                        </div>

                        <!-- Feedback panels -->
                        <div id="answer_panels_{{ part.id }}" class="hidden mt-6">
                            <div class="grid grid-cols-2 gap-6">
                                <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">Your Answer</h4>
                                    <div id="user_answer_{{ part.id }}" class="text-sm text-gray-700 space-y-3"></div>
                                </div>
                                <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-3">Marking Points</h4>
                                    <div id="marking_scheme_{{ part.id }}" class="text-sm text-gray-700 space-y-3"></div>
                                </div>
                            </div>

                            <!-- Common Mistakes Section -->
                            <div id="common_mistakes_{{ part.id }}" class="hidden mt-6">
                                <!-- Common mistakes will be populated by JavaScript -->
                            </div>

                            <!-- Explain Answer Button -->
                            <div class="mt-4 flex justify-center">
                                <button type="button" onclick="explainAnswer({{ part.id }}, {{ question.id }})" class="explain-button inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                                </button>
                            </div>

                            <!-- Explanation Section -->
                            <div id="explanation-{{ part.id }}" class="explanation-container mt-4 hidden">
                                <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation (Based off RI notes)</h4>
                                        <div class="explanation-loading-{{ part.id }} hidden">
                                            <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                        </div>
                                    </div>
                                    <div class="explanation-content-{{ part.id }} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed">
                                        <!-- Explanation content will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            {% endfor %}

            <!-- DOJO Navigation (only show for DOJO questions) -->
            {% if question.is_dojo %}
            <div id="dojo-navigation" class="mt-8 hidden">
                <div class="flex justify-center space-x-4">
                    <button id="prev-dojo-btn"
                            onclick="loadPreviousDojoQuestion()"
                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-arrow-left mr-2"></i>
                        <span id="prev-question-text">Previous</span>
                    </button>
                    <button id="next-dojo-btn"
                            onclick="loadNextDojoQuestion()"
                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="next-question-text">Next</span>
                        <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
                <div id="question-position" class="mt-2 text-center text-sm text-gray-500 hidden"></div>
            </div>
            {% endif %}

            <div class="mt-6 text-center">
                <div class="flex justify-center space-x-4">
                    <a href="{{ url_for('vault') }}" class="inline-flex items-center text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Vault
                    </a>
                    {% if question.is_dojo and question.topic %}
                    <a href="{{ url_for('load_dojo_questions', topic_name=question.topic.name) }}" class="inline-flex items-center text-sm font-semibold leading-6 text-purple-600 hover:text-purple-500">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dojo Topic
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Right Column: Relevant Knowledge -->
        <div class="col-span-3">
            <!-- Relevant Questions Section -->
            {% if question.relevant_questions %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Relevant Knowledge</h3>
                    <div class="space-y-2 overflow-y-auto max-h-[calc(100vh-8rem)]">
                        {% for relevance in question.relevant_questions %}
                        {% set relevant_q = relevance.relevant_question %}
                        <a href="{{ url_for('load_question', question_id=relevant_q.id) }}"
                           class="block p-3 rounded-lg border border-gray-200 hover:border-purple-200 hover:bg-purple-50/50 transition-all duration-200 relative">
                            <div class="flex items-start justify-between">
                                <div class="flex-grow">
                                    <div class="text-sm font-medium text-gray-900">{{ relevant_q.title }}</div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {% if relevant_q.topic %}
                                            {{ relevant_q.topic.name }}
                                        {% else %}
                                            <span class="text-gray-400">No topic</span>
                                        {% endif %}
                                    </div>
                                    <div class="flex items-center mt-2 space-x-2">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                            {{ relevance.relevance_type.replace('_', ' ').title() }}
                                        </span>
                                        <div class="flex items-center">
                                            {% for i in range(relevance.strength) %}
                                            <span class="text-yellow-400 text-xs">★</span>
                                            {% endfor %}
                                            {% for i in range(5 - relevance.strength) %}
                                            <span class="text-gray-300 text-xs">★</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Relevant Knowledge</h3>
                    <div class="text-sm text-gray-500 text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        <p>No relevant knowledge linked to this question yet.</p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Relevant Notes Section -->
            {% if question.relevant_notes %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mt-6">
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Relevant Notes</h3>
                    <div class="space-y-2 overflow-y-auto max-h-[calc(100vh-8rem)]">
                        {% for notes_relevance in question.relevant_notes %}
                        {% set notes_chunk = notes_relevance.notes_chunk %}
                        <a href="/notes/{{ notes_chunk.chapter_id }}#{{ notes_chunk.section_id }}" target="_blank"
                           class="block p-3 rounded-lg border border-gray-200 hover:border-blue-200 hover:bg-blue-50/50 transition-all duration-200 relative">
                            <div class="flex items-start justify-between">
                                <div class="flex-grow">
                                    <div class="text-sm font-medium text-gray-900">{{ notes_chunk.title }}</div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ notes_chunk.filename.replace('.md', '') }}
                                    </div>
                                    <div class="text-xs text-gray-600 mt-1 line-clamp-2">
                                        {{ notes_chunk.content[:150] }}{% if notes_chunk.content|length > 150 %}...{% endif %}
                                    </div>
                                    <div class="flex items-center mt-2 space-x-2">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ notes_relevance.relevance_type.replace('_', ' ').title() }}
                                        </span>
                                        <div class="flex items-center">
                                            {% for i in range(notes_relevance.strength) %}
                                            <span class="text-blue-400 text-xs">★</span>
                                            {% endfor %}
                                            {% for i in range(5 - notes_relevance.strength) %}
                                            <span class="text-gray-300 text-xs">★</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-2 flex-shrink-0">
                                    <i class="fas fa-external-link-alt text-xs text-blue-600"></i>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mt-6">
                <div class="p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-3">Relevant Notes</h3>
                    <div class="text-sm text-gray-500 text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        <p>No relevant notes linked to this question yet.</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>


<script>
    // Global function to render LaTeX in any element
    function renderLatexInElement(element) {
        console.log('renderLatexInElement called with:', element);

        if (!element) {
            console.warn('Element not provided to renderLatexInElement');
            return;
        }

        if (typeof renderMathInElement === 'undefined') {
            console.warn('renderMathInElement not available - KaTeX auto-render not loaded');
            return;
        }

        try {
            console.log('Rendering LaTeX in element:', element.id || element.className);
            renderMathInElement(element, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html'
            });
            console.log('LaTeX rendering completed for element:', element.id || element.className);
        } catch (error) {
            console.error('Error rendering LaTeX:', error);
        }
    }

    // Global function to render LaTeX in multiple elements
    function renderLatexInElements(selector) {
        console.log('renderLatexInElements called with selector:', selector);
        const elements = document.querySelectorAll(selector);
        console.log('Found elements:', elements.length);
        elements.forEach(element => renderLatexInElement(element));
    }

    window.addEventListener('load', function () {
        // Test LaTeX rendering availability
        console.log('Page loaded, testing LaTeX rendering...');
        console.log('renderMathInElement available:', typeof renderMathInElement !== 'undefined');
        console.log('katex available:', typeof katex !== 'undefined');

        // Test with a simple element
        setTimeout(() => {
            const testDiv = document.createElement('div');
            testDiv.innerHTML = 'Test LaTeX: $x^2 + y^2 = z^2$';
            document.body.appendChild(testDiv);
            renderLatexInElement(testDiv);
            console.log('Test div after LaTeX rendering:', testDiv.innerHTML);
            document.body.removeChild(testDiv);
        }, 500);
        console.log('DOM fully loaded');

        // Check if required libraries are loaded
        if (typeof marked === 'undefined') {
            console.error('Marked library is not loaded.');
            return;
        }

        if (typeof katex === 'undefined') {
            console.error('KaTeX library is not loaded.');
            return;
        }

        console.log('Libraries loaded successfully');

        // Additional marked configuration
        marked.use({
            breaks: true,  // Convert line breaks to <br>
            gfm: true,    // Enable GitHub Flavored Markdown
            mangle: false, // Don't mangle email addresses
            headerIds: false // Don't add IDs to headers
        });

        // Function to render LaTeX with KaTeX
        function renderLatex(latex, displayMode) {
            try {
                // Render the LaTeX expression
                const rendered = katex.renderToString(latex, {
                    displayMode: displayMode,
                    throwOnError: false,
                    output: 'html'
                });

                // For inline LaTeX, add a special class to help with styling
                if (!displayMode) {
                    return `<span class="inline-math">${rendered}</span>`;
                }

                return rendered;
            } catch (error) {
                console.error('Error rendering LaTeX:', error, latex);
                return `<span class="text-red-500">Error rendering LaTeX: ${latex}</span>`;
            }
        }



        // Custom function to handle LaTeX in text
        function processLatexInText(text) {
            if (!text) return '';

            // First, escape HTML characters in the text
            let processedText = text
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            // Replace display math ($$...$$)
            processedText = processedText.replace(/\$\$(.*?)\$\$/g, function (match, latex) {
                return renderLatex(latex.trim(), true);
            });

            // Replace inline math ($...$)
            processedText = processedText.replace(/\$([^\$]+?)\$/g, function (match, latex) {
                return renderLatex(latex.trim(), false);
            });

            return processedText;
        }

        // Process text containing both Markdown and LaTeX
        function processText(text) {
            if (!text) return '';

            console.log('Processing text:', text);

            try {
                // Our approach: Process LaTeX first, then Markdown

                // Step 1: Extract and save LaTeX blocks
                let placeholders = [];
                let processedText = text;

                // Extract display math ($$...$$)
                processedText = processedText.replace(/\$\$(.*?)\$\$/gs, function (match, latex) {
                    const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                    placeholders.push({
                        id: id,
                        latex: latex.trim(),
                        displayMode: true
                    });
                    return `<span id="${id}" class="latex-placeholder"></span>`;
                });

                // Extract inline math ($...$)
                processedText = processedText.replace(/\$([^\$\n]+?)\$/g, function (match, latex) {
                    const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                    placeholders.push({
                        id: id,
                        latex: latex.trim(),
                        displayMode: false
                    });
                    return `<span id="${id}" class="latex-placeholder"></span>`;
                });

                // Step 2: Parse the text as Markdown
                let html = marked.parse(processedText);

                // Step 3: Replace placeholders with rendered LaTeX
                placeholders.forEach(placeholder => {
                    const rendered = renderLatex(placeholder.latex, placeholder.displayMode);
                    html = html.replace(
                        new RegExp(`<span id="${placeholder.id}" class="latex-placeholder"></span>`, 'g'),
                        rendered
                    );
                });

                return html;
            } catch (error) {
                console.error('Error in custom processing:', error);

                // Fallback to simpler approach
                try {
                    console.log('Using simpler fallback approach');

                    // Process LaTeX directly
                    const processedWithLatex = processLatexInText(text);

                    // Wrap in a div
                    return `<div>${processedWithLatex}</div>`;
                } catch (fallbackError) {
                    console.error('Fallback approach failed:', fallbackError);

                    // Last resort: just escape the text
                    try {
                        console.log('Using basic text escaping');
                        const escaped = text
                            .replace(/&/g, '&amp;')
                            .replace(/</g, '&lt;')
                            .replace(/>/g, '&gt;')
                            .replace(/"/g, '&quot;')
                            .replace(/'/g, '&#039;');

                        return `<p>${escaped}</p>`;
                    } catch (lastResortError) {
                        console.error('Even basic escaping failed:', lastResortError);
                        return `<p class="text-red-500">Error processing text</p><pre>${text}</pre>`;
                    }
                }
            }
        }

        // Function to update the preview
        function updatePreview(textarea) {
            // Find the preview element (it's the next sibling with class 'live-preview')
            let previewElement = null;
            let currentElement = textarea.nextElementSibling;

            while (currentElement) {
                if (currentElement.classList.contains('live-preview')) {
                    previewElement = currentElement;
                    break;
                }
                currentElement = currentElement.nextElementSibling;
            }

            if (!previewElement) {
                console.error('Preview element not found for textarea:', textarea);
                return;
            }

            const text = textarea.value || '';

            // Process the text and update the preview
            const processedHTML = processText(text);
            previewElement.innerHTML = processedHTML || '<p class="text-gray-400 italic">Preview will appear here</p>';
        }

        // Find all textareas with the latex-content class
        const textareas = document.querySelectorAll('.latex-content');
        console.log('Found textareas:', textareas.length);

        // Initialize each textarea
        textareas.forEach(textarea => {
            console.log('Initializing textarea:', textarea.id);
            // Initial render
            updatePreview(textarea);

            // Add input event listener
            textarea.addEventListener('input', function() {
                updatePreview(this);
            });
        });

        // Render part descriptions
        const partDescriptions = document.querySelectorAll('.part-description');
        console.log('Found part descriptions:', partDescriptions.length);

        partDescriptions.forEach(element => {
            const content = element.getAttribute('data-content');
            if (content) {
                const processedHTML = processText(content);
                element.innerHTML = processedHTML;
            }
        });

        // Render MCQ option content
        const mcqOptions = document.querySelectorAll('.option-content');
        console.log('Found MCQ options:', mcqOptions.length);

        mcqOptions.forEach(element => {
            const content = element.textContent;
            if (content) {
                const processedHTML = processText(content);
                element.innerHTML = processedHTML;
            }
        });

        // Render feedback
        const feedbackContainer = document.getElementById('feedback-container');
        if (feedbackContainer) {
            const feedbackDiv = feedbackContainer.querySelector('.prose');
            const content = feedbackDiv.innerHTML;
            if (content) {
                const processedHTML = processText(content);
                feedbackDiv.innerHTML = processedHTML;
            }
        }
    });

    // List of unsubmittable part IDs (parts that require diagrams)
    const unsubmittableParts = [3, 10, 12, 24, 28, 30, 33, 34, 39, 44, 45];

    // Function to blur unsubmittable parts
    function blurUnsubmittableParts() {
        unsubmittableParts.forEach(partId => {
            // Find the form container for this part
            const form = document.querySelector(`form[data-part-id="${partId}"]`);
            if (form) {
                form.classList.add('unsubmittable-part');
            }

            // Blur the textarea
            const textarea = document.getElementById(`answer_${partId}`);
            if (textarea) {
                textarea.classList.add('unsubmittable-blur');
                textarea.disabled = true;
                textarea.placeholder = 'This part requires a diagram and cannot be submitted online';
            }

            // Blur and disable the submit button
            const submitButton = document.getElementById(`submit-part-${partId}`);
            if (submitButton) {
                submitButton.classList.add('unsubmittable-button');
                submitButton.disabled = true;
                submitButton.innerHTML = 'Diagram Required';
            }

            // Also blur MCQ options if they exist
            const mcqOptions = document.getElementById(`mcq_options_${partId}`);
            if (mcqOptions) {
                mcqOptions.classList.add('unsubmittable-blur');
                // Disable all radio buttons in this part
                const radioButtons = mcqOptions.querySelectorAll('input[type="radio"]');
                radioButtons.forEach(radio => {
                    radio.disabled = true;
                });
            }

            // Blur image upload section
            const imageUpload = document.querySelector(`input[id="image_${partId}"]`);
            if (imageUpload) {
                const uploadLabel = imageUpload.closest('label');
                if (uploadLabel) {
                    uploadLabel.classList.add('unsubmittable-blur');
                }
                imageUpload.disabled = true;
            }

            // Blur calculator if it exists
            const calculator = document.getElementById(`calculator_${partId}`);
            if (calculator) {
                calculator.classList.add('unsubmittable-blur');
            }
        });
    }

    // Call the blur function when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        blurUnsubmittableParts();

        // Load DOJO navigation info if this is a DOJO question
        {% if question.is_dojo %}
            loadDojoNavigationInfo();
        {% endif %}
    });

    function showLoading(event, partId) {
        event.preventDefault();
        const form = event.target;
        const loading = document.getElementById(`loading_${partId}`);
        const correctAnswer = document.getElementById(`correct_answer_${partId}`);

        loading.classList.remove('hidden');
        correctAnswer.classList.remove('hidden');

        return false; // Prevent form submission as we're using submitAllAnswers
    }

    document.addEventListener('DOMContentLoaded', function() {
        // MathQuill initialization is now handled in questionjs.js

        // Add toggle handlers for submissions
        document.querySelectorAll('.toggle-submissions').forEach(button => {
            button.addEventListener('click', function() {
                const partId = this.getAttribute('data-part-id');
                const submissionsList = document.getElementById(`submissions-${partId}`);

                if (submissionsList.classList.contains('hidden')) {
                    submissionsList.classList.remove('hidden');
                    this.textContent = 'Hide';
                } else {
                    submissionsList.classList.add('hidden');
                    this.textContent = 'Show All';
                }
            });
        });

        // Add event listener to save cursor position when clicking in textarea
        document.querySelectorAll('textarea[id^="answer_"]').forEach(textarea => {
            textarea.addEventListener('click', function() {
                const partId = this.id.split('_')[1];
                const calculator = document.getElementById(`calculator_${partId}`);
                if (calculator) {
                    calculator.dataset.cursorPosition = this.selectionStart;
                }
            });

            textarea.addEventListener('keyup', function() {
                const partId = this.id.split('_')[1];
                const calculator = document.getElementById(`calculator_${partId}`);
                if (calculator) {
                    calculator.dataset.cursorPosition = this.selectionStart;
                }
            });
        });
    });

    // Function to insert math symbols
    function insertMath(partId, symbol) {
        const mathField = mathFields[partId];
        if (mathField) {
            if (symbol === '\\sqrt' || symbol === '\\frac' ) {
                mathField.cmd(symbol);
            } else if (symbol === '^') {
                mathField.cmd('^');
                mathField.typedText('');
            } else {
                mathField.write(symbol);
            }
            mathField.focus();
        }
    }

    // Function to insert to textarea at cursor position
    function insertToTextarea(partId) {
        const mathField = mathFields[partId];
        const textarea = document.getElementById(`answer_${partId}`);

        if (mathField && textarea) {
            const latex = mathField.latex();
            if (!latex) return; // Don't insert if empty

            // Save the current cursor position or use the end of text
            const savedStart = textarea.selectionStart || textarea.value.length;
            const savedEnd = textarea.selectionEnd || textarea.value.length;

            // Format the LaTeX for display
            let displayText = latex;
            // If it's a simple expression (no special LaTeX commands), keep it as is
            if (!latex.includes('\\')) {
                displayText = latex;
            } else {
                // Wrap LaTeX expressions in $$ for proper display
                displayText = `$${latex}$`;
            }

            // Insert at cursor position or append to end
            const textBefore = textarea.value.substring(0, savedStart);
            const textAfter = textarea.value.substring(savedEnd);

            // Add spaces around the expression if needed
            const needSpaceBefore = textBefore.length > 0 && !textBefore.endsWith(' ');
            const needSpaceAfter = textAfter.length > 0 && !textAfter.startsWith(' ');

            const newText = textBefore +
                          (needSpaceBefore ? ' ' : '') +
                          displayText +
                          (needSpaceAfter ? ' ' : '') +
                          textAfter;

            // Update textarea while preserving existing content
            textarea.value = newText;

            // Calculate and restore cursor position after the inserted expression
            const newCursorPos = savedStart +
                               (needSpaceBefore ? 1 : 0) +
                               displayText.length +
                               (needSpaceAfter ? 1 : 0);

            textarea.focus();
            textarea.selectionStart = newCursorPos;
            textarea.selectionEnd = newCursorPos;

            // Clear the math input for next expression
            mathField.latex('');
        }
    }

    // Function to toggle calculator
    function toggleCalculator(partId) {
        const calculator = document.getElementById(`calculator_${partId}`);
        const toggleText = document.getElementById(`calc_toggle_text_${partId}`);
        const textarea = document.getElementById(`answer_${partId}`);

        if (calculator.classList.contains('hidden')) {
            calculator.classList.remove('hidden');
            toggleText.textContent = 'Hide Calculator';

            // Save cursor position when opening calculator
            calculator.dataset.cursorPosition = textarea.selectionStart || textarea.value.length;
        } else {
            calculator.classList.add('hidden');
            toggleText.textContent = 'Show Calculator';

            // Restore focus to textarea when closing calculator
            textarea.focus();
            const savedPos = parseInt(calculator.dataset.cursorPosition) || textarea.value.length;
            textarea.selectionStart = savedPos;
            textarea.selectionEnd = savedPos;
        }
    }

    function previewImage(input, previewId) {
        const preview = document.getElementById(previewId);
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.querySelector('img').src = e.target.result;
                preview.classList.remove('hidden');
            }
            reader.readAsDataURL(input.files[0]);
        }
    }

    function toggleTimingDetails(partId, event) {
        // Prevent form submission
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        // Try MCQ, SAQ individual, and SAQ bulk timing detail IDs
        const mcqDetailsDiv = document.getElementById(`timing-details-mcq-${partId}`);
        const saqDetailsDiv = document.getElementById(`timing-details-${partId}`);
        const saqIndividualDetailsDiv = document.getElementById(`timing-details-saq-${partId}`);
        const mcqToggleSpan = document.getElementById(`timing-toggle-mcq-${partId}`);
        const saqToggleSpan = document.getElementById(`timing-toggle-${partId}`);
        const saqIndividualToggleSpan = document.getElementById(`timing-toggle-saq-${partId}`);

        const detailsDiv = mcqDetailsDiv || saqDetailsDiv || saqIndividualDetailsDiv;
        const toggleSpan = mcqToggleSpan || saqToggleSpan || saqIndividualToggleSpan;

        if (detailsDiv && toggleSpan) {
            const currentText = toggleSpan.textContent;
            if (detailsDiv.classList.contains('hidden')) {
                detailsDiv.classList.remove('hidden');
                // Extract timing from current text and show "Hide"
                const timingMatch = currentText.match(/⏱ (\d+ms)/);
                if (timingMatch) {
                    toggleSpan.textContent = '▼ ' + timingMatch[1];
                } else {
                    toggleSpan.textContent = 'Hide Details';
                }
            } else {
                detailsDiv.classList.add('hidden');
                // Extract timing from current text and show "Show"
                const timingMatch = currentText.match(/(\d+ms)/);
                if (timingMatch) {
                    toggleSpan.textContent = '⏱ ' + timingMatch[1];
                } else {
                    toggleSpan.textContent = 'Show Details';
                }
            }
        }

        return false;
    }

    function removeImage(inputId, previewId) {
        document.getElementById(inputId).value = '';
        document.getElementById(previewId).classList.add('hidden');
    }

    // Function to explain the answer using Gemini LLM
    async function explainAnswer(partId, questionId) {
        const explanationDiv = document.getElementById(`explanation-${partId}`);
        const explanationContent = document.querySelector(`.explanation-content-${partId}`);
        const loadingIndicator = document.querySelector(`.explanation-loading-${partId}`);

        if (!explanationDiv || !explanationContent || !loadingIndicator) {
            console.error('Required explanation elements not found');
            return;
        }

        // If already visible, toggle it off and return
        if (!explanationDiv.classList.contains('hidden')) {
            explanationDiv.classList.add('hidden');
            return;
        }

        // Show the explanation div and loading indicator
        explanationDiv.classList.remove('hidden');
        loadingIndicator.classList.remove('hidden');
        explanationContent.innerHTML = '<div class="text-gray-500 text-sm italic">Generating explanation...</div>';

        try {
            // Fetch the explanation from the server
            const response = await fetch(`/explain_answer/${questionId}/${partId}`, {
                method: 'GET'
            });

            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }

            // Set up streaming response with real-time display
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let explanation = '';

            // Hide loading and show content immediately
            loadingIndicator.classList.add('hidden');
            explanationContent.innerHTML = '<div class="streaming-content whitespace-pre-wrap font-mono text-sm"></div>';
            const streamingDiv = explanationContent.querySelector('.streaming-content');

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                explanation += chunk;

                // Show raw text immediately as it streams in
                streamingDiv.textContent = explanation;

                // Scroll to show new content immediately
                explanationContent.scrollTop = explanationContent.scrollHeight;
            }

            // After streaming is complete, apply full formatting
            let formattedExplanation = explanation
                // Add proper styling to headings
                .replace(/^# (.*?)$/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1</h3>')
                .replace(/^## (.*?)$/gm, '<h4 class="text-md font-semibold text-gray-800 mt-3 mb-2">$1</h4>')
                .replace(/^### (.*?)$/gm, '<h5 class="text-sm font-semibold text-gray-800 mt-2 mb-1">$1</h5>')
                // Style bullet points with • symbol
                .replace(/^• (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                // Style numbered lists
                .replace(/^\d+\. (.*?)$/gm, '<li class="ml-4 list-decimal list-inside mb-1">$1</li>')
                // Style traditional markdown lists (fallback)
                .replace(/^\* (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                .replace(/^- (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                // Remove any remaining markdown bold/italic formatting and replace with proper HTML
                .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
                .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
                // Handle code blocks (though we discourage them in prompts)
                .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
                // Wrap paragraphs (but not headings or list items)
                .replace(/^(?!<h|<li|<p|<div|<strong|<em|<code)(.*?)$/gm, '<p class="mb-2 leading-relaxed">$1</p>');

            // Replace with formatted content
            explanationContent.innerHTML = formattedExplanation;

            // Render LaTeX only once at the end
            if (typeof renderMathInElement !== 'undefined') {
                renderMathInElement(explanationContent, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\(', right: '\\)', display: false},
                        {left: '\\[', right: '\\]', display: true}
                    ],
                    throwOnError: false,
                    output: 'html'
                });
            }
        } catch (error) {
            console.error('Error fetching explanation:', error);
            explanationContent.innerHTML = `<p class="text-red-600">Error: ${error.message}</p>`;
        } finally {
            // Hide loading indicator
            loadingIndicator.classList.add('hidden');
        }
    }

    // Modify the existing submitPart function to handle image uploads
    async function submitPart(event, partId) {
        event.preventDefault();
        const form = event.target;

        const submitButton = document.getElementById(`submit-part-${partId}`);
        const loading = document.getElementById(`loading_${partId}`);
        const correctAnswer = document.getElementById(`correct_answer_${partId}`);

        // Disable the submit button and show loading
        submitButton.disabled = true;
        loading.classList.remove('hidden');

        try {
            const formData = new FormData(form);
            formData.append('confidence_level', 'Medium');

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            // Show feedback for this part
            const feedbackDiv = document.querySelector(`#feedback_${partId}`);
            if (feedbackDiv && result.feedback) {
                // Add verdict and score display
                const verdictHtml = `
                    <div class="mb-4 flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">Verdict:</span>
                            <span class="text-sm font-semibold ${result.score == result.max_score ? 'text-green-600' : result.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                ${result.score == result.max_score ? 'Correct' : result.score > 0 ? 'Partial' : 'Incorrect'}
                            </span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">Score:</span>
                            <span class="text-sm font-semibold ${result.score >= result.max_score ? 'text-green-600' : result.score > 0 ? 'text-yellow-600' : 'text-red-600'}">
                                ${result.score}/${result.max_score}
                            </span>
                        </div>
                    </div>
                `;
                feedbackDiv.querySelector('.prose').innerHTML = verdictHtml + result.feedback;
                feedbackDiv.classList.remove('hidden');
            }

            // Show correct answer for this part if the answer was not irrelevant
            if (correctAnswer && !result.feedback.includes('irrelevant')) {
                correctAnswer.classList.remove('hidden');
            }

        } catch (error) {
            console.error('Error submitting answer:', error);
            // Display the error message from the caught error
            alert('An error occurred: ' + error.message);
        } finally {
            // Hide loading and re-enable submit button
            loading.classList.add('hidden');
            submitButton.disabled = false;
        }

        return false;
    }

    function toggleCorrectAnswer(partId) {
        const answerContent = document.getElementById(`answer_content_${partId}`);
        const arrow = document.getElementById(`arrow_${partId}`);
        const button = arrow.parentElement;

        if (answerContent.classList.contains('hidden')) {
            answerContent.classList.remove('hidden');
            arrow.classList.add('rotate-180');
            button.querySelector('span').textContent = 'Hide Correct Answer';
        } else {
            answerContent.classList.add('hidden');
            arrow.classList.remove('rotate-180');
            button.querySelector('span').textContent = 'Show Correct Answer';
        }
    }

    // Function to toggle correct answer visibility for marking points
    function toggleCorrectAnswerVisibility(container) {
        container.classList.toggle('revealed');
    }



    // Function to toggle feedback visibility - reveals ALL marking points for the part
    function toggleFeedbackVisibility(container) {
        // Find the closest marking scheme container to identify the part
        const markingSchemeContainer = container.closest('[id^="marking_scheme_"]') ||
                                     container.closest('[id^="feedback-container"]') ||
                                     container.closest('.space-y-3');

        if (markingSchemeContainer) {
            // Find all feedback containers within this part
            const allFeedbackContainers = markingSchemeContainer.querySelectorAll('.saq-feedback-container');

            // Check if any are already revealed
            const anyRevealed = Array.from(allFeedbackContainers).some(fc => fc.classList.contains('revealed'));

            // If any are revealed, hide all; otherwise reveal all
            allFeedbackContainers.forEach(fc => {
                if (anyRevealed) {
                    fc.classList.remove('revealed');
                } else {
                    fc.classList.add('revealed');
                }
            });
        } else {
            // Fallback to original behavior if we can't find the part container
            container.classList.toggle('revealed');
        }
    }

    async function extractMarkingPoints(partId) {
        try {
            const response = await fetch(`/extract_marking_points/${partId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Refresh the page to show new marking points
                window.location.reload();
            } else {
                alert('Error extracting marking points: ' + data.message);
            }
        } catch (error) {
            alert('Error extracting marking points: ' + error.message);
        }
    }

    async function updateMarkingPoint(markingPointId, field, value) {
        try {
            const response = await fetch(`/update_marking_point/${markingPointId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    [field]: value
                })
            });

            const data = await response.json();

            if (data.status !== 'success') {
                alert('Error updating marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error updating marking point: ' + error.message);
        }
    }

    async function deleteMarkingPoint(markingPointId) {
        if (!confirm('Are you sure you want to delete this marking point?')) {
            return;
        }

        try {
            const response = await fetch(`/delete_marking_point/${markingPointId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Remove the marking point element from the DOM
                const element = document.querySelector(`[data-marking-point-id="${markingPointId}"]`);
                if (element) {
                    element.remove();
                }
            } else {
                alert('Error deleting marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error deleting marking point: ' + error.message);
        }
    }

    async function addNewMarkingPoint(partId) {
        try {
            const response = await fetch(`/add_marking_point/${partId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    description: 'New marking point',
                    score: 1.0,
                    order: document.querySelectorAll(`#marking-points-${partId} > div`).length
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Refresh the page to show the new marking point
                window.location.reload();
            } else {
                alert('Error adding marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error adding marking point: ' + error.message);
        }
    }

    async function moveMarkingPoint(markingPointId, direction) {
        try {
            const response = await fetch(`/move_marking_point/${markingPointId}/${direction}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Refresh the page to show updated order
                window.location.reload();
            } else {
                alert('Error moving marking point: ' + data.message);
            }
        } catch (error) {
            alert('Error moving marking point: ' + error.message);
        }
    }

    // Add this function to generate a color for marking points
    function getMarkingPointColor(index) {
        const colors = [
            'bg-green-100 text-green-800',
            'bg-blue-100 text-blue-800',
            'bg-purple-100 text-purple-800',
            'bg-yellow-100 text-yellow-800',
            'bg-indigo-100 text-indigo-800'
        ];
        return colors[index % colors.length];
    }

    // Function to confirm deletion of a question
    function confirmDelete(questionId) {
        // Create a custom modal dialog
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
                <p class="text-gray-600 mb-6">Are you sure you want to delete this question? This action cannot be undone.</p>
                <div class="flex justify-end space-x-3">
                    <button id="cancel-delete" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors">
                        Cancel
                    </button>
                    <button id="confirm-delete" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        document.getElementById('cancel-delete').addEventListener('click', function() {
            document.body.removeChild(modal);
        });

        document.getElementById('confirm-delete').addEventListener('click', function() {
            window.location.href = `/delete_question/${questionId}`;
        });

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function matchPanelHeights(partId) {
        // Re-match heights for the grid rows after content changes
        const gridRows = document.querySelectorAll('.grid-row');
        const rows = Array.from(gridRows);

        // Get the number of marking points to determine how to pair rows
        const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);
        if (!markingSchemeDiv) return;

        const markingPointElements = markingSchemeDiv.querySelectorAll('.grid-row');
        const numPoints = markingPointElements.length;

        // Match heights for each pair of marking points
        for (let i = 0; i < numPoints; i++) {
            const leftRow = rows[i];
            const rightRow = rows[i + numPoints];
            if (leftRow && rightRow) {
                // Reset heights first
                leftRow.style.height = 'auto';
                rightRow.style.height = 'auto';

                // Then match to the taller one
                setTimeout(() => {
                    const maxHeight = Math.max(leftRow.offsetHeight, rightRow.offsetHeight);
                    leftRow.style.height = maxHeight + 'px';
                    rightRow.style.height = maxHeight + 'px';
                }, 50);
            }
        }
    }

    async function loadHighlightedAnswer(partId, questionId, userAnswerDiv, highlightData, userAnswer) {
        try {
            const response = await fetch(`/highlighted_answer/${questionId}/${partId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    highlight_data: highlightData,
                    user_answer: userAnswer
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'success') {
                // Replace spinner with highlighted answer with fade-in effect
                userAnswerDiv.style.opacity = '0';
                userAnswerDiv.innerHTML = data.answer;

                // Render LaTeX in the user answer
                setTimeout(() => {
                    renderLatexInElement(userAnswerDiv);

                    // Re-match heights after LaTeX rendering and highlighting is complete
                    matchPanelHeights(partId);

                    // Fade in the content
                    userAnswerDiv.style.transition = 'opacity 0.3s ease-in-out';
                    userAnswerDiv.style.opacity = '1';
                }, 100);
            } else {
                // Fallback to plain text if highlighting fails
                const answerText = userAnswer || 'No answer provided';
                userAnswerDiv.style.opacity = '0';
                userAnswerDiv.innerHTML = `
                    <div class="text-sm text-gray-700 whitespace-pre-wrap">${escapeHtml(answerText)}</div>
                `;

                // Still render LaTeX and match heights even in fallback case
                setTimeout(() => {
                    renderLatexInElement(userAnswerDiv);
                    matchPanelHeights(partId);

                    // Fade in the content
                    userAnswerDiv.style.transition = 'opacity 0.3s ease-in-out';
                    userAnswerDiv.style.opacity = '1';
                }, 100);
            }
        } catch (error) {
            console.error('Error loading highlighted answer:', error);
            // Fallback to plain text if highlighting fails
            const answerText = userAnswer || 'No answer provided';
            userAnswerDiv.style.opacity = '0';
            userAnswerDiv.innerHTML = `
                <div class="text-sm text-gray-700 whitespace-pre-wrap">${escapeHtml(answerText)}</div>
                <div class="mt-2 text-xs text-red-500">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    Could not highlight answer. Showing plain text.
                </div>
            `;

            // Still render LaTeX and match heights even in fallback case
            setTimeout(() => {
                renderLatexInElement(userAnswerDiv);
                matchPanelHeights(partId);

                // Fade in the content
                userAnswerDiv.style.transition = 'opacity 0.3s ease-in-out';
                userAnswerDiv.style.opacity = '1';
            }, 100);
        }
    }

    async function submitAnswer(event, partId, questionId) {
        event.preventDefault();
        const form = event.target;
        const inputType = form.getAttribute('data-input-type') || 'saq';

        let answer = '';
        let hasImage = false;

        // Handle different input types
        if (inputType === 'saq') {
            const answerInput = form.querySelector('textarea[name="answer"]');
            const imageInput = form.querySelector('input[type="file"]');

            answer = answerInput ? answerInput.value.trim() : '';
            hasImage = imageInput && imageInput.files && imageInput.files.length > 0;

            if (!answer && !hasImage) {
                alert('Please enter an answer or upload an image before submitting.');
                return false;
            }
        } else if (inputType === 'mcq') {
            const selectedOption = form.querySelector('input[name="answer"]:checked');

            if (!selectedOption) {
                alert('Please select an answer option before submitting.');
                return false;
            }

            answer = selectedOption.value;
        }

        const submitButton = document.getElementById(`submit-part-${partId}`);
        const loading = document.getElementById(`loading_${partId}`);
        const panelsDiv = document.getElementById(`answer_panels_${partId}`);

        // Disable the submit button and show loading
        submitButton.disabled = true;
        loading.classList.remove('hidden');

        try {
            // Create FormData to handle both text and file uploads
            const formData = new FormData();

            if (answer) {
                formData.append('answer', answer);
            }

            if (hasImage) {
                formData.append('image', imageInput.files[0]);
            }

            // Add confidence level if it exists
            const confidenceSelect = form.querySelector('select[name="confidence_level"]');
            if (confidenceSelect) {
                formData.append('confidence_level', confidenceSelect.value);
            }

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
                // Don't set Content-Type header - browser will set it with boundary for multipart/form-data
            });

            // --- START Rate Limit Check ---
            if (response.status === 429) {
                const errorData = await response.json();
                alert(errorData.message || 'Rate limit exceeded. Please wait and try again.'); // Show alert for rate limit
                // Optionally, re-enable the button or provide specific UI feedback here
                return false; // Stop further processing
            }
            // --- END Rate Limit Check ---

            // Check for other non-OK responses
            if (!response.ok) {
                 // Try to get error message from JSON, otherwise use status text
                let errorMessage = `HTTP error! Status: ${response.status}`;
                try {
                    const errorJson = await response.json();
                    errorMessage = errorJson.message || errorMessage;
                } catch (e) {
                    // Ignore if response is not JSON
                }
                throw new Error(errorMessage);
            }

            const data = await response.json();

            // Debug: Log timing data
            console.log('MCQ Response data:', data);
            console.log('MCQ Timing data:', data.timing);

            // Original success logic starts here (assuming data.status exists for success)
            if (data.status === 'success') {
                // Update the UI with the feedback
                submitButton.disabled = false;
                loading.classList.add('hidden');

                const inputType = form.getAttribute('data-input-type') || 'saq';

                // Clear any previous scoring content for this part and show the panels
                panelsDiv.innerHTML = `
                    <div class="grid grid-cols-2 gap-6">
                        <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Your Answer</h4>
                            <div id="user_answer_${partId}" class="text-sm text-gray-700 space-y-3"></div>
                        </div>
                        <div class="p-5 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Marking Points</h4>
                            <div id="marking_scheme_${partId}" class="text-sm text-gray-700 space-y-3"></div>
                        </div>
                    </div>

                    <!-- Common Mistakes Section -->
                    <div id="common_mistakes_${partId}" class="hidden mt-6">
                        <!-- Common mistakes will be populated by JavaScript -->
                    </div>

                    <!-- Explain Answer Button -->
                    <div class="mt-4 flex justify-center">
                        <button type="button" onclick="explainAnswer(${partId}, ${questionId})" class="explain-button inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-lightbulb mr-1.5"></i> Explain Answer
                        </button>
                    </div>

                    <!-- Explanation Section -->
                    <div id="explanation-${partId}" class="explanation-container mt-4 hidden">
                        <div class="p-5 bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-semibold text-gray-900">Key Concepts & Explanation (Based off RI notes)</h4>
                                <div class="explanation-loading-${partId} hidden">
                                    <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                </div>
                            </div>
                            <div class="explanation-content-${partId} prose prose-sm max-w-none text-gray-700 latex-content leading-relaxed">
                                <!-- Explanation content will be loaded here -->
                            </div>
                        </div>
                    </div>
                `;
                panelsDiv.classList.remove('hidden');

                // Get the panels
                const userAnswerDiv = document.getElementById(`user_answer_${partId}`);
                const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);

                if (inputType === 'mcq') {
                    // For MCQ, show a simple feedback message
                    // Update the user answer panel
                    if (userAnswerDiv) {
                        const selectedOption = form.querySelector(`input[name="answer"]:checked`);
                        const optionLabel = selectedOption ?
                            selectedOption.closest('.mcq-option').querySelector('.option-content').innerHTML :
                            'No option selected';

                        userAnswerDiv.innerHTML = `
                            <div class="whitespace-pre-wrap">
                                <p class="mb-2">You selected:</p>
                                <div class="p-3 border rounded-md ${data.is_correct ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300'}">
                                    ${optionLabel}
                                </div>
                            </div>
                            <div class="mt-4 text-sm ${data.is_correct ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}">
                                ${data.feedback}
                            </div>
                            <div class="mt-2 text-sm text-gray-500">Score: ${data.score}/${data.max_score}</div>
                            ${data.timing ?
                                '<div class="mt-3 pt-3 border-t border-gray-100">' +
                                    '<div class="flex items-center justify-between mb-2">' +
                                        '<span class="text-xs text-gray-500">Performance</span>' +
                                        '<button type="button" onclick="toggleTimingDetails(' + partId + ', event)" class="text-xs text-gray-400 hover:text-gray-600 focus:outline-none transition-colors">' +
                                            '<span id="timing-toggle-mcq-' + partId + '">⏱ ' + data.timing.total_duration_ms + 'ms</span>' +
                                        '</button>' +
                                    '</div>' +
                                    '<div id="timing-details-mcq-' + partId + '" class="hidden mt-2 space-y-1">' +
                                        data.timing.steps.map((step, index) =>
                                            '<div class="bg-gray-50 rounded p-2 text-xs">' +
                                                '<div class="flex items-center justify-between">' +
                                                    '<span class="text-gray-600">' + step.name + '</span>' +
                                                    '<span class="text-gray-500">' + step.duration_ms + 'ms</span>' +
                                                '</div>' +
                                                '<div class="mt-1 bg-gray-200 rounded-full h-1">' +
                                                    '<div class="bg-gray-400 h-1 rounded-full" style="width: ' + ((step.duration_ms / data.timing.total_duration_ms) * 100) + '%"></div>' +
                                                '</div>' +
                                            '</div>'
                                        ).join('') +
                                    '</div>' +
                                '</div>'
                                : ''}
                        `;
                    }

                    // For MCQ, we don't show detailed marking points
                    if (markingSchemeDiv) {
                        markingSchemeDiv.innerHTML = `
                            <div class="text-sm text-gray-500">
                                ${data.is_correct ?
                                    '<div class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Correct answer selected</div>' :
                                    '<div class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i> Incorrect answer selected</div>'}
                            </div>
                        `;
                    }
                } else {
                    // For SAQ, show the detailed feedback
                    // --- START MODIFIED CODE ---
                    // Show spinner in user answer panel while loading highlighted answer
                    userAnswerDiv.innerHTML = `
                        <div class="flex items-center justify-center py-8">
                            <div class="flex items-center space-x-3">
                                <div class="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                <span class="text-sm text-gray-600">Highlighting...</span>
                            </div>
                        </div>
                    `;

                    // Load highlighted answer asynchronously (don't await here)
                    const userAnswer = formData.get('answer') || '';
                    loadHighlightedAnswer(partId, questionId, userAnswerDiv, data.highlight_data, userAnswer);
                    // --- END MODIFIED CODE ---

                    // Display the marking scheme with aligned points and vertical color bar indicators
                    markingSchemeDiv.innerHTML = data.marking_points.map((mp, index) => {
                        // --- START REVISED CODE ---
                        // mp.color now holds the border class (e.g., 'border-yellow-400')
                        const borderClass = mp.color || '';
                        // --- END REVISED CODE ---

                    return `
                    <div class="grid-row flex items-start p-3 rounded bg-gray-50 h-full transform transition-all duration-300 hover:bg-gray-100 hover:shadow-sm">
                        <!-- Vertical Color Bar -->
                        <div class="flex-shrink-0 w-1 self-stretch rounded-l-md mr-3 ${borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-300') : 'bg-transparent'}"></div>

                        <div class="flex-grow flex items-start space-x-3">
                            <!-- Achieved/Not Achieved Icon -->
                            <div class="flex-shrink-0 mt-0.5">
                                <span class="flex items-center justify-center h-5 w-5 rounded-full ${mp.achieved ? 'bg-green-100 text-green-600' : mp.partial ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'}">
                                    ${mp.achieved ?
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                                        mp.partial ?
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z" clip-rule="evenodd"></path></svg>' :
                                        '<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path></svg>'
                                    }
                                </span>
                            </div>
                            <!-- Marking Point Text -->
                            <div class="flex-1">
                                <p class="text-sm text-gray-900 font-medium">
                                    [${mp.achieved_score}/${mp.score} marks]
                                    ${mp.partial ? '<span class="ml-1 text-xs text-yellow-600 font-medium">(Partial Credit)</span>' : ''}
                                </p>
                                ${mp.feedback ? `
                                    <div class="saq-feedback-container relative group mt-2" onclick="toggleFeedbackVisibility(this)">
                                        <div class="saq-feedback-hidden text-sm font-medium ${mp.achieved ? 'text-green-700' : mp.partial ? 'text-yellow-700' : 'text-red-700'} bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'} transition-all duration-300">
                                            <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                            <span class="feedback-content">${mp.feedback}</span>
                                        </div>
                                        <div class="saq-hover-hint absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-90 rounded-md opacity-100 transition-opacity duration-300 pointer-events-none">
                                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                </svg>
                                                <span>Click to reveal feedback</span>
                                            </div>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>`;
                }).join('');


                // Add script to ensure heights match
                setTimeout(() => {
                    // Render LaTeX in the marking scheme
                    const markingSchemeDiv = document.getElementById(`marking_scheme_${partId}`);
                    if (markingSchemeDiv) {
                        renderLatexInElement(markingSchemeDiv);
                    }

                    const gridRows = document.querySelectorAll('.grid-row');
                    const rows = Array.from(gridRows);
                    const numPoints = data.marking_points.length;

                    // Match heights for each pair of marking points
                    for (let i = 0; i < numPoints; i++) {
                        const leftRow = rows[i];
                        const rightRow = rows[i + numPoints];
                        if (leftRow && rightRow) {
                            const maxHeight = Math.max(leftRow.offsetHeight, rightRow.offsetHeight);
                            leftRow.style.height = maxHeight + 'px';
                            rightRow.style.height = maxHeight + 'px';
                        }
                    }

                    // Add a subtle animation to show the total score
                    const totalScore = data.marking_points.reduce((sum, mp) => sum + (mp.achieved_score || 0), 0);
                    const maxScore = data.marking_points.reduce((sum, mp) => sum + mp.score, 0);

                    // Create and append the score summary element
                    const scoreSummary = document.createElement('div');
                    scoreSummary.className = 'mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg shadow-sm opacity-0 transform translate-y-4 transition-all duration-500';
                    scoreSummary.innerHTML = `
                        <div class="flex items-center justify-between">
                            <h3 class="text-sm font-medium text-gray-900">Your Score</h3>
                            <div class="text-lg font-bold ${totalScore === maxScore ? 'text-green-600' : totalScore > 0 ? 'text-amber-600' : 'text-red-600'}">
                                ${totalScore} / ${maxScore}
                            </div>
                        </div>
                        <div class="mt-3 relative h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div class="absolute top-0 left-0 h-full bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full" style="width: ${(totalScore / maxScore) * 100}%; transition: width 1s ease-out;"></div>
                        </div>
                        <div class="mt-3 text-xs text-gray-500 text-center">
                            ${totalScore === maxScore ?
                                'Perfect score! Excellent work!' :
                                totalScore > maxScore * 0.7 ?
                                'Great job! You\'re doing well.' :
                                totalScore > 0 ?
                                'Keep practicing, you\'re making progress.' :
                                'Don\'t give up! Try reviewing the concepts again.'}
                        </div>
                    `;

                    // Append to the parent element
                    const parent = document.getElementById(`answer_panels_${partId}`);
                    if (parent) {
                        parent.appendChild(scoreSummary);

                        // Add timing information if available
                        if (data.timing) {
                            const timingDiv = document.createElement('div');
                            timingDiv.className = 'mt-4 pt-3 border-t border-gray-100 opacity-0 transform translate-y-2 transition-all duration-300';
                            timingDiv.innerHTML =
                                '<div class="flex items-center justify-between mb-2">' +
                                    '<span class="text-xs text-gray-500">Performance</span>' +
                                    '<button type="button" onclick="toggleTimingDetails(' + partId + ', event)" class="text-xs text-gray-400 hover:text-gray-600 focus:outline-none transition-colors">' +
                                        '<span id="timing-toggle-saq-' + partId + '">⏱ ' + data.timing.total_duration_ms + 'ms</span>' +
                                    '</button>' +
                                '</div>' +
                                '<div id="timing-details-saq-' + partId + '" class="hidden mt-2 space-y-1">' +
                                    data.timing.steps.map((step, index) =>
                                        '<div class="bg-gray-50 rounded p-2 text-xs">' +
                                            '<div class="flex items-center justify-between">' +
                                                '<span class="text-gray-600">' + step.name + '</span>' +
                                                '<span class="text-gray-500">' + step.duration_ms + 'ms</span>' +
                                            '</div>' +
                                            '<div class="mt-1 bg-gray-200 rounded-full h-1">' +
                                                '<div class="bg-gray-400 h-1 rounded-full" style="width: ' + ((step.duration_ms / data.timing.total_duration_ms) * 100) + '%"></div>' +
                                            '</div>' +
                                        '</div>'
                                    ).join('') +
                                '</div>';

                            parent.appendChild(timingDiv);

                            // Trigger animation for timing div after a delay
                            setTimeout(() => {
                                timingDiv.classList.remove('opacity-0', 'translate-y-2');
                            }, 400);
                        }

                        // Display common mistakes if any were detected
                        if (data.detected_mistakes && data.detected_mistakes.length > 0) {
                            const commonMistakesDiv = document.getElementById(`common_mistakes_${partId}`);
                            if (commonMistakesDiv) {
                                let mistakesHtml = `
                                    <div class="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                        <h4 class="text-sm font-medium text-orange-800 mb-3 flex items-center">
                                            <svg class="h-4 w-4 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                            </svg>
                                            Common Mistakes Detected
                                        </h4>
                                        <div class="space-y-2">
                                `;

                                data.detected_mistakes.forEach((mistake, index) => {
                                    mistakesHtml += `
                                        <div class="relative">
                                            <div class="p-4 bg-white border border-orange-200 rounded">
                                                <div class="flex items-start">
                                                    <svg class="h-4 w-4 text-orange-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                                    </svg>
                                                    <div class="flex-grow">
                                                        <div class="text-sm font-medium text-orange-800 mb-2">Mistake ${index + 1}</div>
                                                        <div class="text-sm text-gray-700 leading-relaxed">${mistake}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                });

                                mistakesHtml += `
                                        </div>
                                    </div>
                                `;

                                commonMistakesDiv.innerHTML = mistakesHtml;
                                commonMistakesDiv.classList.remove('hidden');
                            }
                        }

                        // Trigger animation after a short delay
                        setTimeout(() => {
                            scoreSummary.classList.remove('opacity-0', 'translate-y-4');
                        }, 300);
                    }
                }, 100);
                } // Close the else block for SAQ

            // Removed the 'else' part here as non-success JSON was handled by !response.ok check above
            } else {
              //alert('Error: ' + data.message); // This might be redundant now
            }
        } catch (error) {
            console.error('Error submitting answer:', error);
            // Display the error message from the caught error
            alert('An error occurred: ' + error.message);
        } finally {
            // Hide loading and re-enable submit button
            loading.classList.add('hidden');
            submitButton.disabled = false;
        }

        return false;
    }
</script>

<style>
    /* Enhanced animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }

    .animate-pulse-slow {
        animation: pulse 2s ease-in-out infinite;
    }

    /* Enhanced evidence text styling */
    .evidence-text {
        position: relative;
        border-left: 3px solid currentColor;
        transition: all 0.2s ease;
    }

    .evidence-text:hover {
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Styles for unsubmittable parts */
    .unsubmittable-part {
        position: relative;
        margin-top: 35px;
    }

    .unsubmittable-part::before {
        content: "⚠️ This part requires a diagram and cannot be submitted online";
        position: absolute;
        top: -35px;
        left: 0;
        right: 0;
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        color: #92400e;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        text-align: center;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .unsubmittable-blur {
        filter: blur(3px) !important;
        pointer-events: none !important;
        user-select: none !important;
        transition: filter 0.3s ease;
    }

    .unsubmittable-button {
        background-color: #fca5a5 !important;
        border-color: #f87171 !important;
        color: #7f1d1d !important;
        cursor: not-allowed !important;
        filter: blur(2px) !important;
    }

    /* Next DOJO Question Button Styles */
    .next-dojo-container {
        background: linear-gradient(135deg, #7c3aed, #4f46e5);
        animation: subtle-pulse 3s ease-in-out infinite;
    }

    @keyframes subtle-pulse {
        0%, 100% {
            box-shadow: 0 4px 20px rgba(124, 58, 237, 0.3);
        }
        50% {
            box-shadow: 0 6px 25px rgba(124, 58, 237, 0.4);
        }
    }

    .next-dojo-btn {
        position: relative;
        overflow: hidden;
    }

    .next-dojo-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .next-dojo-btn:hover::before {
        left: 100%;
    }

    .next-dojo-btn:hover {
        transform: translateY(-2px) scale(1.02);
        box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
    }

    /* Improved grid row styling */
    .grid-row {
        position: relative;
        overflow: hidden;
    }

    .grid-row::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right, transparent, rgba(99, 102, 241, 0.3), transparent);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .grid-row:hover::after {
        transform: scaleX(1);
    }

    /* Enhanced form elements */
    textarea:focus {
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        transform: translateY(-1px);
    }

    button {
        position: relative;
        overflow: hidden;
    }

    button::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.7);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    button:focus:not(:active)::after {
        animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .grid-cols-12 {
            grid-template-columns: 1fr;
        }

        .col-span-9, .col-span-3 {
            grid-column: span 12 / span 12;
        }

        .hidden-mobile {
            display: none;
        }

        .px-4 {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .prose img {
            max-width: 100%;
            height: auto;
        }

        .text-lg {
            font-size: 1.1rem;
        }

        .flex-col-mobile {
            flex-direction: column;
        }

        .w-full-mobile {
            width: 100%;
        }

        .mt-4-mobile {
            margin-top: 1rem;
        }

        /* Adjust form elements for touch */
        input, textarea, button, select {
            font-size: 16px; /* Prevents iOS zoom on focus */
            min-height: 44px; /* Better touch targets */
        }

        /* Ensure math elements are properly sized */
        .mathquill-editable {
            max-width: 100%;
            overflow-x: auto;
        }

        /* Improve spacing for mobile */
        .mb-6 {
            margin-bottom: 1rem;
        }

        .p-6 {
            padding: 1rem;
        }
    }
</style>

<script>








    // Clarification modal functions
    function openClarificationModal(questionId) {
        document.getElementById('clarification-question-id').value = questionId;
        document.getElementById('clarification-modal').classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
    }

    function closeClarificationModal() {
        document.getElementById('clarification-modal').classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
        // Reset form
        document.getElementById('clarification-form').reset();
        document.getElementById('clarification-part-id').value = '';
    }

    function openPartClarificationModal(questionId, partId) {
        document.getElementById('clarification-question-id').value = questionId;
        document.getElementById('clarification-part-id').value = partId;
        document.getElementById('clarification-modal').classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
    }

    function sendClarification() {
        const form = document.getElementById('clarification-form');
        const formData = new FormData(form);

        const data = {
            question_id: parseInt(formData.get('question_id')),
            part_id: formData.get('part_id') ? parseInt(formData.get('part_id')) : null,
            subject: formData.get('subject'),
            message: formData.get('message')
        };

        // Validate required fields
        if (!data.subject || !data.message) {
            alert('Please fill in all required fields.');
            return;
        }

        // Show loading state
        const submitBtn = document.querySelector('#clarification-modal button[onclick="sendClarification()"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
        submitBtn.disabled = true;

        fetch('/api/clarifications/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert(data.message);
                closeClarificationModal();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to send clarification. Please try again.');
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }
</script>

<!-- Clarification Modal -->
<div id="clarification-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Ask Teacher for Clarification</h3>
                <button onclick="closeClarificationModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="clarification-form" class="space-y-4">
                <input type="hidden" id="clarification-question-id" name="question_id" value="">
                <input type="hidden" id="clarification-part-id" name="part_id" value="">

                <div>
                    <label for="clarification-subject" class="block text-sm font-medium text-gray-700 mb-1">
                        Subject <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="clarification-subject"
                           name="subject"
                           required
                           placeholder="Brief description of your question"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="clarification-message" class="block text-sm font-medium text-gray-700 mb-1">
                        Your Question <span class="text-red-500">*</span>
                    </label>
                    <textarea id="clarification-message"
                              name="message"
                              required
                              rows="5"
                              placeholder="Please describe what you need help with. Be as specific as possible to help teachers provide the best assistance."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700 font-medium mb-2">
                                What will be shared with teachers:
                            </p>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• Your clarification question and subject</li>
                                <li>• Your latest submission/answer for this question part</li>
                                <li>• Your score and grading information (if available)</li>
                                <li>• The timestamp of your submission</li>
                            </ul>
                            <p class="text-xs text-blue-600 mt-2 italic">
                                This context helps teachers provide more targeted assistance.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button"
                            onclick="closeClarificationModal()"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </button>
                    <button type="button"
                            onclick="sendClarification()"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-paper-plane mr-2"></i>Send Clarification
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Tour Context Handling -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if this question was opened from the DOJO tour
    const urlParams = new URLSearchParams(window.location.search);
    const tourContext = urlParams.get('tour_context');

    if (tourContext === 'dojo') {
        // Add a special banner for tour context
        const questionContainer = document.querySelector('.mx-auto.max-w-7xl');
        if (questionContainer) {
            const tourBanner = document.createElement('div');
            tourBanner.className = 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-4 rounded-lg mb-6 shadow-lg';
            tourBanner.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">🥋</div>
                        <div>
                            <h3 class="font-bold text-lg">DOJO Tour Experience</h3>
                            <p class="text-purple-100 text-sm">You're trying a DOJO question as part of the tour. Feel free to explore and submit an answer!</p>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="continueTour()" class="bg-white text-purple-600 px-4 py-2 rounded-md font-semibold hover:bg-purple-50 transition-colors">
                            Continue Tour
                        </button>
                        <button onclick="finishTour()" class="bg-purple-500 text-white px-4 py-2 rounded-md font-semibold hover:bg-purple-400 transition-colors">
                            Finish Tour
                        </button>
                    </div>
                </div>
            `;
            questionContainer.insertBefore(tourBanner, questionContainer.firstChild);
        }
    }
});

function continueTour() {
    // Mark tour as completed and return to DOJO
    localStorage.setItem('dojoShepherdTourCompleted', 'true');
    localStorage.setItem('globalTourCompleted', 'true');
    window.location.href = '/dojo';
}

function finishTour() {
    // Mark all tours as completed
    localStorage.setItem('dojoShepherdTourCompleted', 'true');
    localStorage.setItem('vaultShepherdTourCompleted', 'true');
    localStorage.setItem('globalTourCompleted', 'true');
    window.location.href = '/dashboard';
}

// DOJO Navigation functionality
async function loadDojoNavigationInfo() {
    try {
        const currentQuestionId = parseInt('{{ question.id }}');

        // Load both next and previous question info
        const [nextResponse, prevResponse] = await Promise.all([
            fetch(`/api/get-next-dojo-question/${currentQuestionId}`),
            fetch(`/api/get-previous-dojo-question/${currentQuestionId}`)
        ]);

        const nextData = await nextResponse.json();
        const prevData = await prevResponse.json();

        const container = document.getElementById('dojo-navigation');
        const nextBtn = document.getElementById('next-dojo-btn');
        const prevBtn = document.getElementById('prev-dojo-btn');
        const nextText = document.getElementById('next-question-text');
        const prevText = document.getElementById('prev-question-text');
        const positionInfo = document.getElementById('question-position');

        // Handle next question
        if (nextData.success) {
            nextText.textContent = 'Next';
            nextBtn.setAttribute('data-next-question-id', nextData.next_question.id);
            nextBtn.disabled = false;

            // Show position info using current_position from API
            positionInfo.textContent = `Question ${nextData.current_position} of ${nextData.total_questions}`;
            positionInfo.classList.remove('hidden');
        } else {
            nextBtn.disabled = true;
        }

        // Handle previous question
        if (prevData.success) {
            prevText.textContent = 'Previous';
            prevBtn.setAttribute('data-prev-question-id', prevData.previous_question.id);
            prevBtn.disabled = false;

            // If we didn't get position info from next, use previous data
            if (!nextData.success) {
                positionInfo.textContent = `Question ${prevData.current_position} of ${prevData.total_questions}`;
                positionInfo.classList.remove('hidden');
            }
        } else {
            prevBtn.disabled = true;
        }

        // Show the navigation container
        container.classList.remove('hidden');

    } catch (error) {
        console.error('Error loading DOJO navigation info:', error);
    }
}

function loadNextDojoQuestion() {
    const button = document.getElementById('next-dojo-btn');
    const nextQuestionId = button.getAttribute('data-next-question-id');

    if (nextQuestionId) {
        // Add loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';

        // Navigate to the next question
        window.location.href = `/vault/${nextQuestionId}`;
    }
}

function loadPreviousDojoQuestion() {
    const button = document.getElementById('prev-dojo-btn');
    const prevQuestionId = button.getAttribute('data-prev-question-id');

    if (prevQuestionId) {
        // Add loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';

        // Navigate to the previous question
        window.location.href = `/vault/${prevQuestionId}`;
    }
}
</script>

{% endblock %}
